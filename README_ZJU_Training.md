# ZJU Dataset Training for Marigold

This document describes how to train <PERSON><PERSON> on the ZJU dataset with the specific data format requirements.

## Data Format

The ZJU dataset has the following specifications:
- **Image size**: 1280×720 pixels
- **Valid region**: Height slice `[:, 720//3:720//4*3]` = `[:, 240:540]` (300 pixels height)
- **Image data**: 0-255.0 range float32, CHW format
- **GT depth**: Actual depth values in meters, encoded with 256.0 multiplier in PNG format

## Files Created

### Training Script
- `train_zju.py` - Main training script with ZJU dataset support

### Configuration Files
- `config/train_marigold_zju.yaml` - Main training configuration
- `config/dataset/train_zju.yaml` - Training dataset configuration
- `config/dataset/val_zju.yaml` - Validation dataset configuration  
- `config/dataset/vis_zju.yaml` - Visualization dataset configuration

### Test Script
- `test_zju_dataset.py` - Test script to verify dataset implementation

## Usage

### Basic Training
```bash
python train_zju.py --config config/train_marigold_zju.yaml
```

### Training with Custom Data Directory
```bash
python train_zju.py --config config/train_marigold_zju.yaml --base_data_dir /path/to/your/data
```

### Training with Custom Output Directory
```bash
python train_zju.py --config config/train_marigold_zju.yaml --output_dir /path/to/output
```

### Resume Training
```bash
python train_zju.py --resume_run /path/to/checkpoint.pth
```

### Training without Wandb
```bash
python train_zju.py --config config/train_marigold_zju.yaml --no_wandb
```

## Data Structure

Your data directory should be organized as follows:
```
/data/Baidu/ZJU-4DRadarCam/data/
├── image/
│   ├── 1687163764634577152.png
│   ├── 1686219069587458048.png
│   └── ...
└── gt_interp/
    ├── 1687163764634577152.png
    ├── 1686219069587458048.png
    └── ...
```

The file lists should be in:
- `data_split/zju/train_list.txt`
- `data_split/zju/val_list.txt`

## Key Features

### ZJU Dataset Class
The `ZJUDataset` class in `train_zju.py` handles:
- Automatic cropping to valid region (240:540 in height)
- Depth decoding from PNG (division by 256.0)
- Proper resizing after cropping
- CHW format handling

### Configuration
- Uses the same training parameters as standard Marigold
- Automatically handles ZJU-specific data format
- Supports all standard Marigold features (validation, visualization, etc.)

## Testing

Before training, you can test the dataset implementation:
```bash
python test_zju_dataset.py
```

This will verify that:
- Dataset can be loaded correctly
- Image cropping is applied properly
- Depth data is decoded correctly
- Tensor shapes are as expected

## Environment Variables

You can set these environment variables instead of using command line arguments:
- `BASE_DATA_DIR` - Base directory for training data
- `BASE_CKPT_DIR` - Base directory for checkpoints

## Notes

1. The training script automatically registers the ZJU dataset class
2. Image cropping is applied before any other processing
3. The valid region cropping reduces height from 720 to 300 pixels
4. Depth values are automatically converted from PNG encoding to meters
5. All standard Marigold augmentations and training procedures are supported
