# ZJU Dataset Training for Marigold - 完整实现总结

## 概述

已成功创建了专门用于ZJU数据集的Marigold训练程序，支持以下数据格式：
- 图片尺寸：1280×720
- 有效区域：[:, 720//3:720//4*3] = [:, 240:540]
- 图像数据：0-255.0范围的float32，CHW格式
- GT深度：实际深度值(米)，通过256.0倍数编码存储在PNG中

## 创建的文件

### 1. 主要训练脚本
- **`train_zju.py`** - 专门的ZJU数据集训练脚本
  - 包含ZJUDataset类，处理特殊的数据格式
  - 自动裁剪到有效区域
  - 正确解码深度数据（除以256.0）
  - 支持所有标准Marigold训练功能

### 2. 配置文件
- **`config/train_marigold_zju.yaml`** - 主训练配置文件
- **`config/dataset/train_zju.yaml`** - 训练数据集配置
- **`config/dataset/val_zju.yaml`** - 验证数据集配置
- **`config/dataset/vis_zju.yaml`** - 可视化数据集配置

### 3. 辅助脚本
- **`test_zju_dataset.py`** - 数据集测试脚本
- **`run_zju_training.sh`** - 便捷的训练启动脚本

### 4. 文档
- **`README_ZJU_Training.md`** - 详细使用说明
- **`ZJU_Training_Summary.md`** - 本总结文档

## 使用方法

### 方法1：直接使用Python脚本
```bash
python train_zju.py --config config/train_marigold_zju.yaml
```

### 方法2：使用便捷脚本
```bash
./run_zju_training.sh
```

### 方法3：自定义参数
```bash
./run_zju_training.sh --data_dir /your/data/path --output_dir /your/output/path --no_wandb
```

## 核心特性

### ZJUDataset类特性
1. **自动区域裁剪**：将720像素高度裁剪到240-540区域（300像素）
2. **深度解码**：PNG值除以256.0得到实际米制深度
3. **格式处理**：正确处理CHW格式的图像数据
4. **尺寸调整**：裁剪后调整到300×1280分辨率

### 训练配置
- 使用与标准Marigold相同的训练参数
- 支持验证和可视化
- 支持断点续训
- 支持Wandb日志记录

## 数据结构要求

```
/data/Baidu/ZJU-4DRadarCam/data/
├── image/
│   ├── 1687163764634577152.png
│   └── ...
└── gt_interp/
    ├── 1687163764634577152.png
    └── ...
```

文件列表：
- `data_split/zju/train_list.txt`
- `data_split/zju/val_list.txt`

## 测试验证

运行测试脚本验证实现：
```bash
python test_zju_dataset.py
```

## 技术实现细节

### 数据处理流程
1. 加载原始1280×720图像
2. 裁剪到有效区域[:, 240:540]
3. 调整大小到300×1280
4. 深度数据除以256.0解码
5. 应用标准Marigold预处理

### 关键代码特性
- 继承自BaseDepthDataset
- 重写_read_depth_file方法处理PNG编码
- 重写_load_rgb_data和_load_depth_data处理裁剪
- 重写_training_preprocess处理ZJU特定需求

## 环境变量

可选设置：
- `BASE_DATA_DIR` - 数据目录
- `BASE_CKPT_DIR` - 检查点目录

## 注意事项

1. 确保数据目录结构正确
2. 确保train_list.txt和val_list.txt存在
3. 图像裁剪会将高度从720减少到300像素
4. 深度值自动从PNG编码转换为米制
5. 支持所有标准Marigold增强和训练程序

## 成功标志

如果看到以下输出，说明设置正确：
```
ZJU training started at [timestamp]
Using ZJU dataset for training
✓ Dataset created successfully with [N] samples
device = cuda
```

训练将自动开始，并在指定的输出目录中保存检查点和日志。
