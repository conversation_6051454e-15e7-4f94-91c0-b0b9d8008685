# Last modified: 2024-02-08
#
# Copyright 2023 <PERSON><PERSON> Ke, ETH Zurich. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------
# If you find this code useful, we kindly ask you to cite our paper in your work.
# Please find bibtex at: https://github.com/prs-eth/Marigold#-citation
# If you use or adapt this code, please attribute to https://github.com/prs-eth/marigold.
# More information about the method can be found at https://marigoldmonodepth.github.io
# --------------------------------------------------------------------------


from .base_depth_dataset import BaseDepthDataset, DepthFileNameMode
import numpy as np

class SelfDataset(BaseDepthDataset):
    def __init__(
        self,
        **kwargs,
    ) -> None:
        super().__init__(
            # Hypersim data parameter
            min_depth=1e-5,
            max_depth=100.0,
            has_filled_depth=False,
            name_mode=DepthFileNameMode.rgb_i_d,
            **kwargs,
        )

    def _read_depth_file(self, rel_path):
        depth_in = self._read_image(rel_path)
        # Decode Hypersim depth
        # print("self")
        depth_decoded = depth_in #/ 1000.0
        # print(depth_decoded.shape,np.max(depth_decoded),np.min(depth_decoded))
        return depth_decoded


from enum import Enum
import random
from torchvision.transforms import InterpolationMode, Resize

class DatasetMode(Enum):
    RGB_ONLY = "rgb_only"
    EVAL = "evaluate"
    TRAIN = "train"


class Self_VALDataset(BaseDepthDataset):
    def __init__(
        self,
        **kwargs,
    ) -> None:
        super().__init__(
            # Hypersim data parameter
            min_depth=1e-5,
            max_depth=100.0,
            has_filled_depth=False,
            name_mode=DepthFileNameMode.rgb_i_d,
            **kwargs,
        )

    def _read_depth_file(self, rel_path):
        depth_in = self._read_image(rel_path)
        # Decode Hypersim depth
        # print("self")
        depth_decoded = depth_in #/ 1000.0
        # print(depth_decoded.shape,np.max(depth_decoded),np.min(depth_decoded))
        return depth_decoded
    
    def __getitem__(self, index):
        rasters, other = self._get_data_item(index)
        if DatasetMode.TRAIN == self.mode:
            rasters = self._training_preprocess(rasters)
        # merge
        
        # print("come in!")
        total_width=1280
        crop_width=400
        start = random.randint(0, total_width - crop_width)
        end = start + crop_width
        # {print(v.shape) for k, v in rasters.items()}
        # print("裁切处理！")
        resize_transform = Resize(
            size=(480,640), interpolation=InterpolationMode.NEAREST_EXACT
        )
        rasters = {k: v[:,:,start:end] for k, v in rasters.items()}
        import matplotlib.pyplot as plt
        import numpy as np
        # for k, v in rasters.items():
        #     # 判断通道数
        #     if v.shape[0] == 1:
        #         # 灰度图
        #         image = v.squeeze().numpy()  # 移除通道维度
        #         # 归一化到 [0, 1]
        #         if image.dtype == bool:
        #             image = image.astype(np.float32)
        #         image = (image - image.min()) / (image.max() - image.min())
        #         plt.imshow(image, cmap='gray')
        #         plt.title(f"{k} (Grayscale)")
        #         plt.axis('off')
        #         plt.show()
        #     elif v.shape[0] == 3:
        #         # RGB 图
        #         image = np.transpose(v.numpy(), (1, 2, 0))  # 调整维度顺序
        #         # 归一化到 [0, 1]
        #         if image.dtype == bool:
        #             image = image.astype(np.float32)
        #         image = (image - image.min()) / (image.max() - image.min())
        #         plt.imshow(image)
        #         plt.title(f"{k} (RGB)")
        #         plt.axis('off')
        #         plt.show()
        #     else:
        #         # 其他情况
        #         print(f"未知的通道数: {v.shape[0]}，跳过可视化")  
        rasters = {k: resize_transform(v) for k, v in rasters.items()}
        outputs = rasters
        outputs.update(other)
        return outputs
