#!/usr/bin/env python3
"""
Test script for ZJU dataset implementation
"""

import sys
import os
import torch
import numpy as np
from omegaconf import OmegaConf

# Add the current directory to Python path
sys.path.append('.')

from train_zju import ZJUDataset
from src.dataset import DatasetMode
from src.util.depth_transform import get_depth_normalizer

def test_zju_dataset():
    """Test ZJU dataset loading and processing"""
    
    print("Testing ZJU dataset implementation...")
    
    # Create depth normalizer
    cfg_normalizer = OmegaConf.create({
        "type": "scale_shift_depth",
        "clip": True,
        "norm_min": -1.0,
        "norm_max": 1.0,
        "min_max_quantile": 0.02
    })
    depth_transform = get_depth_normalizer(cfg_normalizer)
    
    # Test dataset configuration
    dataset_config = {
        "mode": DatasetMode.TRAIN,
        "filename_ls_path": "data_split/zju/train_list.txt",
        "dataset_dir": "/data/Baidu/ZJU-4DRadarCam/data/",
        "disp_name": "zju_test",
        "depth_transform": depth_transform,
        "resize_to_hw": [300, 1280],
        "augmentation_args": {"lr_flip_p": 0.5}
    }
    
    try:
        # Create dataset instance
        dataset = ZJUDataset(**dataset_config)
        print(f"✓ Dataset created successfully with {len(dataset)} samples")
        
        # Test loading a single sample
        if len(dataset) > 0:
            sample = dataset[0]
            print(f"✓ Sample loaded successfully")
            
            # Check sample structure
            print("Sample keys:", list(sample.keys()))
            
            # Check tensor shapes
            for key, value in sample.items():
                if isinstance(value, torch.Tensor):
                    print(f"  {key}: {value.shape} ({value.dtype})")
                else:
                    print(f"  {key}: {type(value)} - {value}")
            
            # Verify cropping was applied correctly
            if 'rgb_norm' in sample:
                rgb_shape = sample['rgb_norm'].shape
                expected_height = 300  # After cropping and resizing
                expected_width = 1280
                if rgb_shape[1] == expected_height and rgb_shape[2] == expected_width:
                    print(f"✓ Image cropping and resizing applied correctly: {rgb_shape}")
                else:
                    print(f"⚠ Unexpected image shape: {rgb_shape}, expected: (3, {expected_height}, {expected_width})")
            
            # Check depth data
            if 'depth_raw_linear' in sample:
                depth_shape = sample['depth_raw_linear'].shape
                print(f"✓ Depth data loaded: {depth_shape}")
                depth_values = sample['depth_raw_linear']
                print(f"  Depth range: {depth_values.min():.3f} - {depth_values.max():.3f}")
                
        else:
            print("⚠ Dataset is empty")
            
    except Exception as e:
        print(f"✗ Error testing dataset: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("✓ ZJU dataset test completed successfully!")
    return True

if __name__ == "__main__":
    test_zju_dataset()
